import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import { getUserRole, ADMIN_ROLES, setUserRole, initializeAdminSystem } from '../services/admin';

/**
 * Admin Test Helper Component
 * Helps verify admin access and provides testing instructions
 */
export default function AdminTestHelper({ onClose }) {
  const { currentUser } = useAuth();
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const [assigning, setAssigning] = useState(false);

  useEffect(() => {
    if (!currentUser) return;

    const checkAdminAccess = async () => {
      try {
        const role = await getUserRole(currentUser.uid);
        setUserRole(role);
      } catch (error) {
        console.error('Error checking admin access:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAdminAccess();
  }, [currentUser]);

  const handleManualAdminAssignment = async () => {
    if (!currentUser) return;

    setAssigning(true);
    try {
      console.log('Manually assigning platform owner role...');

      // Force assign platform owner role
      await setUserRole(currentUser.uid, ADMIN_ROLES.PLATFORM_OWNER, 'MANUAL_ASSIGNMENT');

      // Re-initialize admin system
      await initializeAdminSystem(currentUser.uid, currentUser.email);

      // Refresh role
      const newRole = await getUserRole(currentUser.uid);
      setUserRole(newRole);

      console.log('Manual admin assignment completed');
      alert('Platform owner role assigned successfully!');
    } catch (error) {
      console.error('Error in manual admin assignment:', error);
      alert('Error assigning admin role: ' + error.message);
    } finally {
      setAssigning(false);
    }
  };

  if (loading) {
    return (
      <div className="admin-test-modal">
        <div className="admin-test-content">
          <div className="loading">Checking admin access...</div>
        </div>
      </div>
    );
  }

  const isAdmin = userRole && userRole.role !== ADMIN_ROLES.USER;
  const isPlatformOwner = userRole && userRole.role === ADMIN_ROLES.PLATFORM_OWNER;

  return (
    <div className="admin-test-modal" onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="admin-test-content">
        <div className="admin-test-header">
          <h2>🛡️ Admin Access Verification</h2>
          <button className="close-test-btn" onClick={onClose}>✕</button>
        </div>

        <div className="admin-test-body">
          <div className="user-info-section">
            <h3>Current User Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <strong>Email:</strong> {currentUser?.email || 'Not available'}
              </div>
              <div className="info-item">
                <strong>User ID:</strong> {currentUser?.uid || 'Not available'}
              </div>
              <div className="info-item">
                <strong>Admin Role:</strong> 
                <span className={`role-indicator ${userRole?.role || 'user'}`}>
                  {userRole?.role?.replace('_', ' ').toUpperCase() || 'USER'}
                </span>
              </div>
            </div>
          </div>

          <div className="access-status-section">
            <h3>Admin Access Status</h3>
            {isPlatformOwner ? (
              <div className="status-card success">
                <div className="status-icon">👑</div>
                <div className="status-content">
                  <h4>Platform Owner Access Confirmed!</h4>
                  <p>You have full administrative privileges on NAROOP.</p>
                  <ul>
                    <li>✅ Admin button should be visible in the header</li>
                    <li>✅ Full access to admin dashboard</li>
                    <li>✅ Can assign/remove all admin roles</li>
                    <li>✅ Complete content moderation control</li>
                    <li>✅ User management capabilities</li>
                  </ul>
                </div>
              </div>
            ) : isAdmin ? (
              <div className="status-card partial">
                <div className="status-icon">🛡️</div>
                <div className="status-content">
                  <h4>Admin Access Detected</h4>
                  <p>You have {userRole.role.replace('_', ' ')} privileges.</p>
                  <p>Admin button should be visible in the header.</p>
                </div>
              </div>
            ) : (
              <div className="status-card error">
                <div className="status-icon">❌</div>
                <div className="status-content">
                  <h4>No Admin Access</h4>
                  <p>You are logged in as a regular user.</p>
                  <p>Expected platform owner email: <EMAIL></p>
                  <p>Your email: {currentUser?.email}</p>
                </div>
              </div>
            )}
          </div>

          <div className="testing-instructions">
            <h3>Testing Instructions</h3>
            
            <div className="test-section">
              <h4>🛡️ Admin Access Testing</h4>
              <ol>
                <li>Look for the <strong>🛡️ Admin</strong> button in the top header</li>
                <li>Click the Admin button to open the dashboard</li>
                <li>Verify you can see: Overview, Content, Reports, Admin Users, User Mgmt, Logs tabs</li>
                <li>Test content moderation by approving/rejecting pending stories</li>
                <li>Try assigning moderator roles to other users</li>
              </ol>
            </div>

            <div className="test-section">
              <h4>👥 Friend Request Testing</h4>
              <ol>
                <li>Go to the Friends section (👥 Friends button)</li>
                <li>Click on the "Find Friends" tab</li>
                <li>Try searching for users by their email addresses</li>
                <li>Test sending friend requests to found users</li>
                <li>Check that search tips are displayed below the search box</li>
              </ol>
            </div>

            <div className="test-section">
              <h4>🔍 What to Look For</h4>
              <ul>
                <li><strong>Admin Button:</strong> Should appear in header next to Privacy button</li>
                <li><strong>Search Functionality:</strong> Should find users by exact email match</li>
                <li><strong>Search Tips:</strong> Should show helpful guidance below search box</li>
                <li><strong>Role Badge:</strong> Your role should show as "PLATFORM OWNER" in admin dashboard</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="admin-test-footer">
          {!isPlatformOwner && (
            <button
              className="manual-admin-btn"
              onClick={handleManualAdminAssignment}
              disabled={assigning}
              style={{
                backgroundColor: '#d4af37',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: 'bold',
                cursor: assigning ? 'not-allowed' : 'pointer',
                marginRight: '12px',
                opacity: assigning ? 0.6 : 1
              }}
            >
              {assigning ? 'Assigning...' : '🛡️ Force Assign Platform Owner Role'}
            </button>
          )}
          <button className="close-test-btn-large" onClick={onClose}>
            Close Test Helper
          </button>
        </div>
      </div>
    </div>
  );
}

// Add some basic inline styles
const styles = `
.admin-test-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
}

.admin-test-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.admin-test-header {
  background: linear-gradient(135deg, #b8860b, #daa520);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-test-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.info-grid {
  display: grid;
  gap: 1rem;
  margin: 1rem 0;
}

.info-item {
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #b8860b;
}

.role-indicator {
  padding: 0.25rem 0.5rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
  margin-left: 0.5rem;
}

.role-indicator.platform_owner {
  background: linear-gradient(135deg, #e63946, #daa520);
  color: white;
}

.role-indicator.user {
  background: #6c757d;
  color: white;
}

.status-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  margin: 1rem 0;
}

.status-card.success {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  border: 2px solid #28a745;
}

.status-card.error {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  border: 2px solid #dc3545;
}

.status-icon {
  font-size: 2rem;
}

.test-section {
  margin: 1.5rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #b8860b;
}

.close-test-btn, .close-test-btn-large {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.close-test-btn-large {
  background: #b8860b;
  color: white;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
}

.admin-test-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #dee2e6;
  text-align: center;
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
